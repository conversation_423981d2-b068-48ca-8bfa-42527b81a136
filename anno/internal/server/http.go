package server

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"github.com/davecgh/go-spew/spew"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/transport"
	"gitlab.rp.konvery.work/platform/apis/middleware"
	"io"
	"strings"
	"time"

	"anno/internal/conf"
	"anno/internal/service"
	"context"

	"github.com/go-kratos/kratos/v2/log"
	middleware2 "github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	"gitlab.rp.konvery.work/platform/apis/anno/v1"
	"gitlab.rp.konvery.work/platform/apis/middleware/common"
)

// NewHTTPServer new a HTTP server.
func NewHTTPServer(c *conf.Server,
	cfgs *service.ConfigsService,
	jobs *service.JobsService,
	lots *service.LotsService,
	lottpls *service.LottplsService,
	labelclz *service.LabelclzService,
	labelwidgets *service.LabelwidgetsService,
	orders *service.OrdersService,
	projects *service.ProjectsService,
	skills *service.SkillsService,
	grant *service.BizgrantsService,
	specgrant *service.SpecgrantsService,
	logger log.Logger) *http.Server {
	mws := common.Middlewares(logger,
		middleware.Auth(),
		middleware.LoadUser(noauthFilters),
	)
	var opts = []http.ServerOption{
		http.Middleware(mws...),
		http.RequestDecoder(CustomizeRequestEncoder),
	}
	if len(c.CorsOrigins) > 0 {
		opts = append(opts, http.Filter(handlers.CORS(
			handlers.AllowCredentials(),
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE", "PATCH"}),
			handlers.AllowedOrigins(strings.Split(c.CorsOrigins, ",")),
		)))
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)

	handleHealth(srv)
	anno.RegisterConfigsHTTPServer(srv, cfgs)
	anno.RegisterJobsHTTPServer(srv, jobs)
	anno.RegisterLotsHTTPServer(srv, lots)
	anno.RegisterLottplsHTTPServer(srv, lottpls)
	anno.RegisterLabelclzHTTPServer(srv, labelclz)
	anno.RegisterLabelwidgetsHTTPServer(srv, labelwidgets)
	anno.RegisterOrdersHTTPServer(srv, orders)
	anno.RegisterProjectsHTTPServer(srv, projects)
	anno.RegisterSkillsHTTPServer(srv, skills)
	anno.RegisterBizgrantsHTTPServer(srv, grant)
	anno.RegisterSpecgrantsHTTPServer(srv, specgrant)
	return srv
}

func handleHealth(srv *http.Server) {
	route := srv.Route("/")
	route.GET("/healthz", func(ctx http.Context) error {
		ctx.String(200, "OK")
		return nil
	})
}

func CustomizeRequestEncoder(r *http.Request, v interface{}) error {
	spew.Dump("---> 1111111 <---")
	spew.Dump("---> CustomizeRequestEncoder Content-Encoding: ", r.Header.Get("Content-Encoding"))
	// 检查Content-Encoding头是否指示gzip压缩
	t := time.Now()
	if r.Header.Get("Content-Encoding") == "gzip" {
		// 创建gzip读取器
		gzipReader, err := gzip.NewReader(r.Body)
		if err != nil {
			return errors.BadRequest("CODEC", err.Error())
		}
		defer gzipReader.Close()

		// 读取解压后的数据
		uncompressedData, err := io.ReadAll(gzipReader)
		if err != nil {
			return errors.BadRequest("CODEC", err.Error())
		}
		spew.Dump("---> uzip data len:", len(string(uncompressedData)))
		// 重置请求体为解压后的数据
		r.Body = io.NopCloser(bytes.NewReader(uncompressedData))
		r.ContentLength = int64(len(uncompressedData))
		r.Header.Set("Content-Type", "application/json")
	}
	fmt.Println("---> unzip req time: ", time.Now().Sub(t))
	err := http.DefaultRequestDecoder(r, v)
	if err != nil {
		log.Errorw("CODEC", err, "req", r.Body)
	}
	return err
}

// gzipDecompressMiddleware 是处理ZIP压缩数据的中间件
func gzipDecompressMiddleware() middleware2.Middleware {
	return func(handler middleware2.Handler) middleware2.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				spew.Dump("---> 2222222 <---")
				spew.Dump(tr.RequestHeader().Get("Content-Encoding"))
				// 获取HTTP请求对象
				//rt, ok := tr.(*http.Transport)
				//r := rt.Request()
				//if ok {
				//	// 检查Content-Encoding头
				//	if r.Header.Get("Content-Encoding") == "gzip" {
				//		// 获取原始请求体
				//		requestBody, err := ioutil.ReadAll(r.Body)
				//		if err != nil {
				//			return nil, err
				//		}
				//
				//		// 解压缩请求体
				//		gzipReader, err := gzip.NewReader(bytes.NewReader(requestBody))
				//		if err != nil {
				//			return nil, err
				//		}
				//		defer gzipReader.Close()
				//
				//		// 读取解压后的数据
				//		uncompressedData, err := ioutil.ReadAll(gzipReader)
				//		if err != nil {
				//			return nil, err
				//		}
				//
				//		// 重置请求体为解压后的数据
				//		r.Body = ioutil.NopCloser(bytes.NewReader(uncompressedData))
				//	}
				//}
			}
			return handler(ctx, req)
		}
	}
}
