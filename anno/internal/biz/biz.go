package biz

import (
	"bytes"
	"compress/gzip"
	"context"
	json2 "encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"strings"

	"anno/workflow/common"

	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/google/wire"
	"github.com/spf13/cast"
	"gitlab.rp.konvery.work/platform/pkg/data/field"
	"gitlab.rp.konvery.work/platform/pkg/data/repo"
	"gitlab.rp.konvery.work/platform/pkg/data/serial"
	"gitlab.rp.konvery.work/platform/pkg/download"
	"gitlab.rp.konvery.work/platform/pkg/upload"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewProjectsBiz, NewLabelwidgetsBiz, NewJobsBiz, NewLotsBiz, NewSkillsBiz,
	NewRolesBiz, NewLottplsBiz, NewLabelclzBiz, NewOrdersBiz, NewBizgrantsBiz, NewSpecgrantsBiz,
	NewConfigBiz, NewJobSkipAnnotationRepoBiz)

type JSON = datatypes.JSON
type DeletedAt = gorm.DeletedAt
type Tx = *gorm.DB
type FieldMask = field.Mask
type TxAction func(ctx context.Context, v any) error

func JSONCodec() encoding.Codec { return encoding.GetCodec("json") }

type StrMap = serial.Map[string, string]

func init() {
	json.MarshalOptions.EmitUnpopulated = true
	json.MarshalOptions.UseProtoNames = true
}

type Pager = repo.Pager
type PageToken = repo.PageToken
type ListFilter = repo.ListFilter

type ValidValue[T any] struct {
	Value T
	Valid bool
}

func NewValidValue[T any](v T) ValidValue[T] {
	return ValidValue[T]{
		Value: v,
		Valid: true,
	}
}

func (o *ValidValue[T]) Set(v T) {
	o.Value = v
	o.Valid = true
}

type BackgroundTask interface {
	SignalEvent(ctx context.Context, ev *common.Event) error
}

func ShouldSaveBigValuesInDB() bool {
	return cast.ToBool(os.Getenv("DBG_SAVE_BIG_VALUES_IN_DB"))
}

func UploadData[T any](ctx context.Context, data T, key string) (*upload.UploadResult, error) {
	dataBytes, err := JSONCodec().Marshal(data)
	if err != nil {
		return nil, err
	}

	reader := bytes.NewReader(dataBytes)
	return upload.Upload(ctx, key, reader, nil)
}

func UploadData2[T any](ctx context.Context, data T, key string, opts *upload.Options) (*upload.UploadResult, error) {
	dataBytes, err := JSONCodec().Marshal(data)
	if err != nil {
		return nil, err
	}

	// 数据zip压缩上传
	var reader io.Reader
	if GetSegmentation3dFlag(ctx) {
		gzipData, err := CompressGzipData(dataBytes, key)
		if err != nil {
			return nil, fmt.Errorf("failed compress gzip data: %w", err)
		}
		key = BuildUploadGzipKey(key)
		reader = bytes.NewReader(gzipData)
	} else {
		reader = bytes.NewReader(dataBytes)
	}
	return upload.Upload(ctx, key, reader, opts)
}

func DownloadData[T any](ctx context.Context, uri string) (data *T, err error) {
	fmt.Println("---> download data uri: ", uri)
	fpath, err := download.Download(ctx, &download.File{URI: uri})
	if err != nil {
		return nil, fmt.Errorf("failed to download file: %w", err)
	}
	defer os.RemoveAll(fpath)
	fmt.Println("file: ", fpath)
	fdata, err := os.ReadFile(fpath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}
	// 是否gzip数据 解压缩
	if strings.HasSuffix(uri, ".gz") || isGzipData(fdata) {
		fdata, err = DecompressGzipData(fdata)
		if err != nil {
			return nil, fmt.Errorf("failed to decompress file: %w", err)
		}
	}
	if err := JSONCodec().Unmarshal(fdata, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal data to elements: %w", err)
	}
	return data, nil
}

func isGzipData(data []byte) bool {
	return len(data) > 2 && data[0] == 0x1F && data[1] == 0x8B
}

// CompressGzipData compresses the input data and returns the compressed byte slice.
func CompressGzipData(data []byte, key string) ([]byte, error) {
	var zBuf bytes.Buffer
	zw := gzip.NewWriter(&zBuf)

	// Setting the Header fields is optional.
	// draft.json
	zw.Name = path.Base(key)

	if _, err := zw.Write(data); err != nil {
		return nil, fmt.Errorf("failed to write data to gzip writer: %w", err)
	}
	if err := zw.Close(); err != nil {
		return nil, fmt.Errorf("failed to close gzip writer: %w", err)
	}

	return zBuf.Bytes(), nil
}

// DecompressGzipData decompresses the input GZIP data and returns the decompressed byte slice.
func DecompressGzipData(data []byte) ([]byte, error) {
	zr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer zr.Close()

	decompressedData, err := io.ReadAll(zr)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress gzip data: %w", err)
	}

	return decompressedData, nil
}

func BuildUploadGzipKey(key string) string {
	if strings.HasSuffix(key, ".json") {
		key = strings.TrimSuffix(key, ".json") + ".gz"
	} else {
		key += ".gz"
	}
	return key
}

type segmentation3dFlagKey struct{}

func WithSegmentation3dFlag(ctx context.Context, enable bool) context.Context {
	return context.WithValue(ctx, segmentation3dFlagKey{}, enable)
}

func GetSegmentation3dFlag(ctx context.Context) bool {
	v := ctx.Value(segmentation3dFlagKey{})
	if v != nil {
		return v.(bool)
	}
	return false
}

// HasSegmentation3d 判断 json.RawMessage 是否包含有效 segmentation3d 数据
func HasSegmentation3d(raw json2.RawMessage) bool {
	if len(raw) == 0 {
		return false
	}
	// 去掉前导空白
	b := bytes.TrimLeft(raw, " \t\r\n")
	if len(b) == 0 {
		return false
	}
	// null
	if bytes.HasPrefix(b, []byte("null")) {
		return false
	}
	// 空数组 []
	if b[0] == '[' {
		for i := 1; i < len(b); i++ {
			c := b[i]
			if c == ' ' || c == '\t' || c == '\r' || c == '\n' {
				continue
			}
			if c == ']' {
				return false
			}
			return true
		}
		return false
	}
	return true
}

const SysTagPrefix = "sys."

func IsSysTag(tag string) bool { return strings.HasPrefix(tag, SysTagPrefix) }
